# FinTrack Quick Start Guide

## Prerequisites
- Docker installed and running
- Java 21
- Maven 3.6+

## Database Setup (SQL Server)

### 1. Start SQL Server Container
```bash
sudo docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=SqlServer123!" \
  -p 1433:1433 --name sqlserver2022 -d \
  mcr.microsoft.com/mssql/server:2022-latest
```

### 2. Verify SQL Server is Running
```bash
# Check container status
sudo docker ps

# Test connection
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'SqlServer123!' -C -Q "SELECT @@VERSION"
```

### 3. Create Application Database
```bash
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'SqlServer123!' -C -Q "CREATE DATABASE FinTrackDB;"
```

## Application Configuration

### Update application.properties
```properties
# Database Configuration
spring.datasource.url=************************************************************************************************
spring.datasource.username=sa
spring.datasource.password=SqlServer123!
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
```

## Development Commands

### Database Management
```bash
# Start SQL Server
sudo docker start sqlserver2022

# Stop SQL Server
sudo docker stop sqlserver2022

# Connect to database
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'SqlServer123!' -d FinTrackDB -C

# View container logs
sudo docker logs sqlserver2022
```

### Application Development
```bash
# Run application
mvn spring-boot:run

# Run tests
mvn test

# Build application
mvn clean package

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## Connection Details
- **Host**: localhost
- **Port**: 1433
- **Database**: FinTrackDB
- **Username**: sa
- **Password**: SqlServer123!

## Useful SQL Queries

### Database Information
```sql
-- List all databases
SELECT name FROM sys.databases;

-- Check database size
SELECT 
    DB_NAME() AS DatabaseName,
    (SELECT SUM(size) FROM sys.database_files WHERE type = 0) * 8 / 1024 AS DataSizeMB;

-- List all tables
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';
```

### User Management
```sql
-- Create application user
CREATE LOGIN fintrack_user WITH PASSWORD = 'FinTrack2024!';
CREATE USER fintrack_user FOR LOGIN fintrack_user;
ALTER ROLE db_datareader ADD MEMBER fintrack_user;
ALTER ROLE db_datawriter ADD MEMBER fintrack_user;
```

## Troubleshooting

### Common Issues
1. **Port 1433 already in use**: Stop other SQL Server instances
2. **Container won't start**: Check Docker logs with `sudo docker logs sqlserver2022`
3. **Connection timeout**: Ensure container is fully started (may take 30-60 seconds)

### Reset Database
```bash
# Stop and remove container
sudo docker stop sqlserver2022
sudo docker rm sqlserver2022

# Start fresh container
sudo docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=SqlServer123!" \
  -p 1433:1433 --name sqlserver2022 -d \
  mcr.microsoft.com/mssql/server:2022-latest
```

## Next Steps
1. Set up Flyway for database migrations
2. Configure Spring Security
3. Implement user authentication
4. Create API endpoints

For detailed configuration, see `docs/DATABASE_SETUP.md`
