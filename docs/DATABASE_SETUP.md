# Database Setup - Microsoft SQL Server

## Overview
This document describes the Microsoft SQL Server configuration for the FinTrack application. The database runs in a Docker container to ensure compatibility and ease of deployment.

## Configuration Details

### SQL Server Version
- **Version**: Microsoft SQL Server 2022 (RTM-CU20) Developer Edition
- **Build**: 16.0.4205.1 (X64)
- **Platform**: Linux (Ubuntu 22.04.5 LTS)
- **Deployment**: Docker Container

### Connection Information
- **Host**: localhost
- **Port**: 1433
- **Database**: (to be created per application needs)
- **Username**: sa (System Administrator)
- **Password**: SqlServer123!
- **Container Name**: sqlserver2022

### Docker Configuration

#### Container Details
```bash
# Container Image
mcr.microsoft.com/mssql/server:2022-latest

# Container Name
sqlserver2022

# Port Mapping
Host: 1433 -> Container: 1433

# Environment Variables
ACCEPT_EULA=Y
MSSQL_SA_PASSWORD=SqlServer123!
```

#### Docker Commands

**Start SQL Server Container:**
```bash
sudo docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=SqlServer123!" \
  -p 1433:1433 --name sqlserver2022 -d \
  mcr.microsoft.com/mssql/server:2022-latest
```

**Check Container Status:**
```bash
sudo docker ps
```

**Stop Container:**
```bash
sudo docker stop sqlserver2022
```

**Start Existing Container:**
```bash
sudo docker start sqlserver2022
```

**Remove Container:**
```bash
sudo docker rm sqlserver2022
```

**View Container Logs:**
```bash
sudo docker logs sqlserver2022
```

### SQL Server Tools

#### sqlcmd Command Line Tool
Location: `/opt/mssql-tools18/bin/sqlcmd`

**Basic Connection:**
```bash
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'SqlServer123!' -C
```

**Execute Query:**
```bash
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'SqlServer123!' -C -Q "SELECT @@VERSION"
```

**Connect to Specific Database:**
```bash
/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'SqlServer123!' -d FinTrackDB -C
```

#### Available Tools
- **sqlcmd**: Command-line query tool
- **bcp**: Bulk copy utility (located at `/opt/mssql-tools18/bin/bcp`)

### Application Configuration

#### Spring Boot application.properties
```properties
# Database Configuration
spring.datasource.url=************************************************************************************************
spring.datasource.username=sa
spring.datasource.password=SqlServer123!
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
spring.jpa.properties.hibernate.format_sql=true

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
```

#### Maven Dependencies
Add to `pom.xml`:
```xml
<dependency>
    <groupId>com.microsoft.sqlserver</groupId>
    <artifactId>mssql-jdbc</artifactId>
    <scope>runtime</scope>
</dependency>
```

### Database Setup Steps

#### 1. Create Application Database
```sql
-- Connect as sa user
CREATE DATABASE FinTrackDB;
GO

-- Verify database creation
SELECT name FROM sys.databases WHERE name = 'FinTrackDB';
GO
```

#### 2. Create Application User (Recommended)
```sql
USE FinTrackDB;
GO

-- Create login
CREATE LOGIN fintrack_user WITH PASSWORD = 'FinTrack2024!';
GO

-- Create user in database
CREATE USER fintrack_user FOR LOGIN fintrack_user;
GO

-- Grant necessary permissions
ALTER ROLE db_datareader ADD MEMBER fintrack_user;
ALTER ROLE db_datawriter ADD MEMBER fintrack_user;
ALTER ROLE db_ddladmin ADD MEMBER fintrack_user;
GO
```

#### 3. Update Application Configuration
If using dedicated user instead of sa:
```properties
spring.datasource.username=fintrack_user
spring.datasource.password=FinTrack2024!
```

### Security Considerations

#### Password Security
- **Current Password**: SqlServer123! (Development only)
- **Production**: Use strong, unique passwords
- **Recommendation**: Use environment variables for passwords

#### Network Security
- Container exposes port 1433 to localhost only
- For production, consider using internal Docker networks
- Implement firewall rules as needed

#### User Permissions
- sa user has full administrative privileges
- Consider creating dedicated application users with minimal required permissions
- Regularly review and audit user permissions

### Backup and Recovery

#### Manual Backup
```sql
BACKUP DATABASE FinTrackDB 
TO DISK = '/var/opt/mssql/data/FinTrackDB.bak'
WITH FORMAT, INIT;
```

#### Restore Database
```sql
RESTORE DATABASE FinTrackDB 
FROM DISK = '/var/opt/mssql/data/FinTrackDB.bak'
WITH REPLACE;
```

#### Docker Volume Backup
```bash
# Create volume for persistent data
sudo docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=SqlServer123!" \
  -p 1433:1433 --name sqlserver2022 \
  -v sqlserver_data:/var/opt/mssql \
  -d mcr.microsoft.com/mssql/server:2022-latest
```

### Troubleshooting

#### Common Issues

**1. Container Won't Start**
```bash
# Check container logs
sudo docker logs sqlserver2022

# Check if port is already in use
sudo netstat -tlnp | grep 1433
```

**2. Connection Refused**
```bash
# Verify container is running
sudo docker ps

# Check if SQL Server is ready
sudo docker exec sqlserver2022 /opt/mssql-tools18/bin/sqlcmd \
  -S localhost -U sa -P 'SqlServer123!' -Q "SELECT 1"
```

**3. Authentication Failed**
- Verify password is correct
- Ensure MSSQL_SA_PASSWORD environment variable is set
- Check if account is locked

#### Performance Monitoring
```sql
-- Check active connections
SELECT 
    session_id,
    login_name,
    host_name,
    program_name,
    status
FROM sys.dm_exec_sessions 
WHERE is_user_process = 1;

-- Check database size
SELECT 
    DB_NAME() AS DatabaseName,
    (SELECT SUM(size) FROM sys.database_files WHERE type = 0) * 8 / 1024 AS DataSizeMB,
    (SELECT SUM(size) FROM sys.database_files WHERE type = 1) * 8 / 1024 AS LogSizeMB;
```

### Migration from SQL Server 2017

The original SQL Server 2017 installation had compatibility issues with Ubuntu 24.04 due to missing OpenLDAP 2.5 libraries. The Docker-based SQL Server 2022 solution provides:

- **Better Compatibility**: No host OS library dependencies
- **Easier Management**: Container-based deployment
- **Consistent Environment**: Same setup across different systems
- **Simplified Updates**: Easy to upgrade SQL Server versions

### Next Steps

1. **Database Schema**: Implement Flyway or Liquibase for schema migrations
2. **Connection Pooling**: Configure HikariCP for optimal performance
3. **Monitoring**: Set up database monitoring and alerting
4. **Backup Strategy**: Implement automated backup procedures
5. **Security Hardening**: Create dedicated application users and implement proper access controls

---

**Last Updated**: August 4, 2025
**SQL Server Version**: 2022 (RTM-CU20)
**Documentation Version**: 1.0
