# FinTrack Application Configuration Template
# Copy this file to .env and update with your actual values

# Database Configuration
DATABASE_URL=************************************************************************************************
DATABASE_USERNAME=sa
DATABASE_PASSWORD=SqlServer123!

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION=86400000

# Server Configuration
SERVER_PORT=8080
SERVER_SERVLET_CONTEXT_PATH=/api/v1

# Logging Configuration
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_FINTRACK=DEBUG

# AWS Configuration (Optional - for production deployment)
AWS_REGION=us-east-1
AWS_S3_BUCKET=fintrack-documents-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Email Configuration (Optional)
SPRING_MAIL_HOST=smtp.gmail.com
SPRING_MAIL_PORT=587
SPRING_MAIL_USERNAME=<EMAIL>
SPRING_MAIL_PASSWORD=your-app-password

# Development Settings
SPRING_PROFILES_ACTIVE=dev
SPRING_JPA_SHOW_SQL=false
SPRING_JPA_DDL_AUTO=validate

# Security Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Monitoring and Health
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,prometheus
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized
